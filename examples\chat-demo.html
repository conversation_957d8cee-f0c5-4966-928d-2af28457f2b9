<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天组件演示</title>
    <style>

    </style>
</head>

<body>


    <!-- 聊天组件容器 -->
    <chat-widget style="display: none;"></chat-widget>

    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>
    <script>
        let sdk = null;

        // 初始化SDK
        WebSDK.init({
            // hksttUrl: 'ws://*************:8001',
            hksttUrl: 'ws://*************:20096',
            aiServerUrl: 'http://*************:8000',
            // aiServerUrl: 'http://*************:8000',
            ttsUrl: 'http://*************:8000',
            deviceId: 'chat-demo-device',         // 示例：自定义设备ID
            organizationId: 206,                  // 示例：自定义组织ID
            debug: true
        }).then(sdkInstance => {
            sdk = sdkInstance;
            window.sdk = sdk;
            sdk.onNotification('notifications/newUser', (params) => {
                console.log('%%%检测到新用户:', params);
                handleNewUser(params);
            });
            // 监听用户输入通知
            sdk.onNotification('notifications/userInput', (params) => {
                console.log('%%%用户输入:', params.userInput);
            });

            // 监听AI响应通知  
            sdk.onNotification('notifications/aiResponse', (params) => {
                console.log('%%%%AI响应:', params.message);
            });

            // 监听状态通知
            sdk.onNotification('notifications/status', (params) => {
                console.log('%%%%状态:', params.message);
            });


        }).catch(error => {
            console.error('SDK初始化失败:', error);
        });
    </script>
</body>

</html>